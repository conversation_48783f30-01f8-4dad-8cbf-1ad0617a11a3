---
import Layout from '../layouts/Layout.astro';

const title = "Simple, Transparent Pricing - QRAnalytica";
const description = "Choose the perfect plan for your QR code analytics needs. No hidden fees, no subscriptions. Pay once, use forever.";
const canonicalURL = "https://qranalytica.com/pricing";

// Structured Data for Pricing
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "QRAnalytica",
  "description": "Professional QR code analytics platform with all features included",
  "offers": [
    {
      "@type": "Offer",
      "name": "Starter Package",
      "price": "29",
      "priceCurrency": "USD",
      "description": "100 QR codes + 10,000 scans with all features"
    },
    {
      "@type": "Offer",
      "name": "Professional Package",
      "price": "59",
      "priceCurrency": "USD",
      "description": "500 QR codes + 50,000 scans with all features"
    },
    {
      "@type": "Offer",
      "name": "Business Package",
      "price": "79",
      "priceCurrency": "USD",
      "description": "600 QR codes + 60,000 scans with all features"
    },
    {
      "@type": "Offer",
      "name": "Enterprise Package",
      "price": "199",
      "priceCurrency": "USD",
      "description": "2,000 QR codes + 200,000 scans with all features"
    }
  ]
};
---

<Layout title={title} description={description}>
  <!-- Structured Data -->
  <script type="application/ld+json" is:inline set:html={JSON.stringify(structuredData)} />

  <!-- Hero Section -->
  <section class="relative py-24 bg-gradient-to-b from-slate-50 to-white">
    <div class="container mx-auto px-4 text-center">
      <!-- Badge -->
      <div class="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-8">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        No subscriptions, pay once
      </div>

      <!-- Main Headline -->
      <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6 max-w-4xl mx-auto leading-tight">
        Simple, Transparent
        <span class="text-primary">Pricing</span>
      </h1>

      <!-- Subheadline -->
      <p class="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
        Choose the perfect plan for your QR code analytics needs. No hidden fees, no subscriptions. Pay once, use forever.
      </p>

      <!-- Key Benefits -->
      <div class="flex flex-wrap justify-center gap-4 mb-16">
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          Credits never expire
        </div>
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          Instant activation
        </div>
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          30-day money back
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Plans -->
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Choose Your Plan
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Start with any plan and scale as you grow. All plans include full analytics, unlimited QR codes, and 24/7 support.
        </p>
      </div>

      <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-6 max-w-7xl mx-auto">
        <!-- Starter Plan -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Starter</h3>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900">$29</span>
              <span class="text-gray-500 text-sm ml-1">one-time</span>
            </div>
            <p class="text-gray-600 text-sm">Perfect for testing</p>
            <div class="mt-3 text-sm text-gray-500">
              <span class="font-medium">100</span> QR codes • <span class="font-medium">10,000</span> scans
            </div>
          </div>

          <div class="space-y-3 mb-6">
            <div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">All Features Included:</div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Dynamic QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Custom design & branding</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Real-time analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Geographic insights</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>API access & exports</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>24/7 support</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-gray-900 text-white py-2.5 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors text-center block">
            Get Started
          </a>
        </div>

        <!-- Professional Plan (TARGET - Most Popular) -->
        <div class="bg-white rounded-xl border-2 border-primary p-6 hover:shadow-xl transition-all duration-300 relative transform scale-105 shadow-lg">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-gradient-to-r from-primary to-secondary text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">🔥 Most Popular</span>
          </div>

          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Professional</h3>
            <div class="mb-2">
              <span class="text-3xl font-bold text-primary">$59</span>
              <span class="text-gray-500 text-sm ml-1">one-time</span>
            </div>
            <div class="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium inline-block mb-2">
              Best Value • Only $0.12 per QR code
            </div>
            <p class="text-gray-600 text-sm">Perfect for growing businesses</p>
            <div class="mt-3 text-sm text-gray-500">
              <span class="font-medium">500</span> QR codes • <span class="font-medium">50,000</span> scans
            </div>
          </div>

          <div class="space-y-3 mb-6">
            <div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">All Features Included:</div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Dynamic QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Custom design & branding</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Real-time analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Geographic insights</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>API access & exports</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>24/7 support</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-gradient-to-r from-primary to-secondary text-white py-3 px-4 rounded-lg font-semibold hover:shadow-lg transition-all text-center block">
            Choose Professional
          </a>
        </div>

        <!-- Business Plan (DECOY) -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Business</h3>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900">$79</span>
              <span class="text-gray-500 text-sm ml-1">one-time</span>
            </div>
            <div class="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-xs font-medium inline-block mb-2">
              $0.13 per QR code
            </div>
            <p class="text-gray-600 text-sm">For established businesses</p>
            <div class="mt-3 text-sm text-gray-500">
              <span class="font-medium">600</span> QR codes • <span class="font-medium">60,000</span> scans
            </div>
          </div>

          <div class="space-y-3 mb-6">
            <div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">All Features Included:</div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Dynamic QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Custom design & branding</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Real-time analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Geographic insights</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>API access & exports</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>24/7 support</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-gray-600 text-white py-2.5 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors text-center block">
            Choose Business
          </a>
        </div>

        <!-- Enterprise Plan (ANCHOR) -->
        <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl border border-gray-700 p-6 hover:shadow-lg transition-all duration-300 text-white relative">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-gray-900 px-3 py-1 rounded-full text-xs font-bold">⭐ Premium</span>
          </div>

          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold mb-2">Enterprise</h3>
            <div class="mb-2">
              <span class="text-3xl font-bold text-yellow-400">$199</span>
              <span class="text-gray-400 text-sm ml-1">one-time</span>
            </div>
            <div class="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium inline-block mb-2">
              Best Value • Only $0.10 per QR code
            </div>
            <p class="text-gray-400 text-sm">For large organizations</p>
            <div class="mt-3 text-sm text-gray-400">
              <span class="font-medium">2,000</span> QR codes • <span class="font-medium">200,000</span> scans
            </div>
          </div>

          <div class="space-y-3 mb-6">
            <div class="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3">All Features Included:</div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Dynamic QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Custom design & branding</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Real-time analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Geographic insights</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>API access & exports</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>24/7 support</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-gray-900 py-3 px-4 rounded-lg font-bold hover:shadow-lg transition-all text-center block">
            Choose Enterprise
          </a>
        </div>
      </div>

      <!-- Strategic Comparison -->
      <div class="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 border border-blue-200">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Smart Value Comparison</h3>
          <p class="text-gray-600">See why Professional offers the best value per QR code</p>
        </div>

        <div class="grid md:grid-cols-4 gap-4 text-center">
          <div class="bg-white rounded-lg p-4 border">
            <div class="text-lg font-bold text-gray-900 mb-1">Starter</div>
            <div class="text-2xl font-bold text-gray-600 mb-1">$0.29</div>
            <div class="text-xs text-gray-500">per QR code</div>
          </div>
          <div class="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-lg p-4 border-2 border-primary relative">
            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
              <span class="bg-green-500 text-white px-2 py-0.5 rounded text-xs font-bold">BEST VALUE</span>
            </div>
            <div class="text-lg font-bold text-primary mb-1">Professional</div>
            <div class="text-2xl font-bold text-primary mb-1">$0.12</div>
            <div class="text-xs text-gray-500">per QR code</div>
          </div>
          <div class="bg-white rounded-lg p-4 border">
            <div class="text-lg font-bold text-gray-900 mb-1">Business</div>
            <div class="text-2xl font-bold text-orange-600 mb-1">$0.13</div>
            <div class="text-xs text-gray-500">per QR code</div>
          </div>
          <div class="bg-gray-900 rounded-lg p-4 border border-gray-700">
            <div class="text-lg font-bold text-yellow-400 mb-1">Enterprise</div>
            <div class="text-2xl font-bold text-yellow-400 mb-1">$0.10</div>
            <div class="text-xs text-gray-400">per QR code</div>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            💡 <strong>Professional plan</strong> gives you 5x more QR codes than Starter for only 2x the price!
          </p>
        </div>
      </div>

      <!-- All Features Included -->
      <div class="mt-12 bg-white rounded-xl p-8 shadow-sm border">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Every Plan Includes All Features</h3>
          <p class="text-gray-600">No feature restrictions. Only QR code and scan limits differ.</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div>
            <h4 class="font-semibold text-gray-900 mb-4">QR Code Features</h4>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Dynamic QR codes
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Custom design & colors
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Logo embedding
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Bulk download
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-semibold text-gray-900 mb-4">Analytics Features</h4>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Real-time tracking
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Geographic insights
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Device & OS data
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Export to Excel
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-semibold text-gray-900 mb-4">Support & Access</h4>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                24/7 support
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Full API access
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                White-label options
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Custom integrations
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- FAQ Section -->
  <section class="py-20 bg-slate-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Frequently Asked Questions
        </h2>
        <p class="text-lg text-gray-600">
          Everything you need to know about our pricing
        </p>
      </div>

      <div class="max-w-3xl mx-auto space-y-6">
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Do all plans include the same features?</h3>
          <p class="text-gray-600">Yes! Every plan includes all features - dynamic QR codes, custom branding, real-time analytics, API access, and 24/7 support. Only the number of QR codes and scans differ.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Why is Professional the best value?</h3>
          <p class="text-gray-600">Professional gives you 5x more QR codes than Starter for only 2x the price ($0.12 vs $0.29 per QR code). It's the sweet spot for most businesses.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">What's included in each QR code?</h3>
          <p class="text-gray-600">Each QR code includes 100 scans. If you need more scans for a specific QR code, you can purchase additional scan capacity separately.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Do the QR codes expire?</h3>
          <p class="text-gray-600">No, your QR codes never expire and will continue working forever. You own them permanently once purchased.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Can I change my plan later?</h3>
          <p class="text-gray-600">You can purchase additional QR codes anytime by buying another plan. All your existing QR codes remain active and you get access to more capacity.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Is there a money-back guarantee?</h3>
          <p class="text-gray-600">Yes, we offer a 30-day money-back guarantee on all plans. If you're not satisfied, contact us for a full refund.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-gradient-to-br from-primary to-secondary">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
        Choose your plan and start creating professional QR codes with detailed analytics today.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
        <a href="/tool/qr-code-generator" class="inline-flex items-center px-8 py-4 bg-white text-primary font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg shadow-lg">
          Start with Professional
        </a>
        <a href="/tool/qr-code-generator" class="inline-flex items-center px-8 py-4 bg-white/10 text-white font-semibold rounded-lg hover:bg-white/20 transition-colors text-lg border border-white/20">
          Try Starter Plan
        </a>
      </div>

      <p class="text-white/80 text-sm">
        No subscriptions • Credits never expire • 30-day money-back guarantee
      </p>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4 text-center">
      <div class="bg-slate-50 rounded-xl p-8 max-w-2xl mx-auto">
        <h3 class="text-2xl font-bold text-gray-900 mb-4">Need Help Choosing?</h3>
        <p class="text-gray-600 mb-6">
          Our team is here to help you find the perfect plan for your needs.
        </p>
        <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg font-semibold hover:bg-primary/90 transition-colors">
          Contact Support
        </a>
      </div>
    </div>
  </section>
</Layout>