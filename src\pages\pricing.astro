---
import Layout from '../layouts/Layout.astro';

const title = "Simple, Transparent Pricing - QRAnalytica";
const description = "Choose the perfect plan for your QR code analytics needs. No hidden fees, no subscriptions. Pay once, use forever.";
const canonicalURL = "https://qranalytica.com/pricing";

// Structured Data for Pricing
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "QRAnalytica",
  "description": "Credit-based QR code analytics platform - 1 credit = 1 QR code + 100 scans",
  "offers": [
    {
      "@type": "Offer",
      "name": "Starter Package",
      "price": "19",
      "priceCurrency": "USD",
      "description": "200 credits = 200 QR codes + 20,000 scans"
    },
    {
      "@type": "Offer",
      "name": "Professional Package",
      "price": "45",
      "priceCurrency": "USD",
      "description": "500 credits = 500 QR codes + 50,000 scans"
    },
    {
      "@type": "Offer",
      "name": "Enterprise Package",
      "price": "80",
      "priceCurrency": "USD",
      "description": "1,000 credits = 1,000 QR codes + 100,000 scans"
    },
    {
      "@type": "Offer",
      "name": "Scale Package",
      "price": "199",
      "priceCurrency": "USD",
      "description": "3,000 credits = 3,000 QR codes + 300,000 scans"
    }
  ]
};
---

<Layout title={title} description={description}>
  <!-- Structured Data -->
  <script type="application/ld+json" is:inline set:html={JSON.stringify(structuredData)} />

  <!-- Hero Section -->
  <section class="relative py-24 bg-gradient-to-b from-slate-50 to-white">
    <div class="container mx-auto px-4 text-center">
      <!-- Badge -->
      <div class="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-8">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        No subscriptions, pay once
      </div>

      <!-- Main Headline -->
      <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6 max-w-4xl mx-auto leading-tight">
        Simple, Transparent
        <span class="text-primary">Pricing</span>
      </h1>

      <!-- Subheadline -->
      <p class="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
        Choose the perfect plan for your QR code analytics needs. No hidden fees, no subscriptions. Pay once, use forever.
      </p>

      <!-- Key Benefits -->
      <div class="flex flex-wrap justify-center gap-4 mb-16">
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          Credits never expire
        </div>
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          Instant activation
        </div>
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          30-day money back
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Plans -->
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Choose Your Plan
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Start with any plan and scale as you grow. All plans include full analytics, unlimited QR codes, and 24/7 support.
        </p>
      </div>

      <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-6 max-w-7xl mx-auto">
        <!-- Starter Plan -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
          <div class="text-center">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Starter</h3>
            <div class="mb-4">
              <span class="text-3xl font-bold text-gray-900">$19</span>
              <span class="text-gray-500 text-sm ml-1">one-time</span>
            </div>
            <p class="text-gray-600 text-sm mb-6">Perfect for small projects</p>
          </div>

          <div class="space-y-3 mb-6">
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>200 QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>20,000 total scans</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Basic analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Email support</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-gray-900 text-white py-2.5 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors text-center block">
            Get Started
          </a>
        </div>

        <!-- Professional Plan (Most Popular) -->
        <div class="bg-white rounded-xl border-2 border-primary p-6 hover:shadow-lg transition-all duration-300 relative">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-primary text-white px-3 py-1 rounded-full text-xs font-medium">Most Popular</span>
          </div>

          <div class="text-center">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Professional</h3>
            <div class="mb-4">
              <span class="text-3xl font-bold text-gray-900">$45</span>
              <span class="text-gray-500 text-sm ml-1">one-time</span>
            </div>
            <p class="text-gray-600 text-sm mb-6">Best for growing businesses</p>
          </div>

          <div class="space-y-3 mb-6">
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>500 QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>50,000 total scans</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Advanced analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Priority support</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Custom branding</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-primary text-white py-2.5 px-4 rounded-lg font-medium hover:bg-primary/90 transition-colors text-center block">
            Choose Professional
          </a>
        </div>

        <!-- Enterprise Plan -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 relative">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-secondary text-white px-3 py-1 rounded-full text-xs font-medium">Best Value</span>
          </div>

          <div class="text-center">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Enterprise</h3>
            <div class="mb-4">
              <span class="text-3xl font-bold text-gray-900">$80</span>
              <span class="text-gray-500 text-sm ml-1">one-time</span>
            </div>
            <p class="text-gray-600 text-sm mb-6">For large organizations</p>
          </div>

          <div class="space-y-3 mb-6">
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>1,000 QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>100,000 total scans</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Premium analytics</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>24/7 phone support</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>API access</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-secondary text-white py-2.5 px-4 rounded-lg font-medium hover:bg-secondary/90 transition-colors text-center block">
            Choose Enterprise
          </a>
        </div>

        <!-- Scale Plan -->
        <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl border border-gray-700 p-6 hover:shadow-lg transition-all duration-300 text-white">
          <div class="text-center">
            <h3 class="text-xl font-semibold mb-2">Scale</h3>
            <div class="mb-4">
              <span class="text-3xl font-bold">$199</span>
              <span class="text-gray-400 text-sm ml-1">one-time</span>
            </div>
            <p class="text-gray-400 text-sm mb-6">Maximum value & savings</p>
          </div>

          <div class="space-y-3 mb-6">
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>3,000 QR codes</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>300,000 total scans</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>White-label solution</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Dedicated account manager</span>
            </div>
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Custom integrations</span>
            </div>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-white text-gray-900 py-2.5 px-4 rounded-lg font-medium hover:bg-gray-100 transition-colors text-center block">
            Choose Scale
          </a>
        </div>
      </div>

      <!-- Value Proposition -->
      <div class="mt-16 bg-slate-50 rounded-xl p-8">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Why Choose QRAnalytica?</h3>
          <p class="text-gray-600">Everything you need to create, track, and analyze QR codes</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h4 class="font-semibold text-gray-900 mb-2">Instant Setup</h4>
            <p class="text-sm text-gray-600">Create and deploy QR codes in seconds. No complex setup required.</p>
          </div>

          <div class="text-center">
            <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h4 class="font-semibold text-gray-900 mb-2">Real-time Analytics</h4>
            <p class="text-sm text-gray-600">Track scans, locations, devices, and more with detailed insights.</p>
          </div>

          <div class="text-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h4 class="font-semibold text-gray-900 mb-2">Enterprise Security</h4>
            <p class="text-sm text-gray-600">Bank-level security with SSL encryption and secure data storage.</p>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- FAQ Section -->
  <section class="py-20 bg-slate-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Frequently Asked Questions
        </h2>
        <p class="text-lg text-gray-600">
          Everything you need to know about our pricing
        </p>
      </div>

      <div class="max-w-3xl mx-auto space-y-6">
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">How does the credit system work?</h3>
          <p class="text-gray-600">Each credit gives you 1 QR code with 100 scans included. You can create unlimited QR codes and track detailed analytics for each scan.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Do credits expire?</h3>
          <p class="text-gray-600">No, credits never expire. Buy once and use them whenever you need them. There are no monthly fees or recurring charges.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">What happens if I exceed 100 scans per QR code?</h3>
          <p class="text-gray-600">Your QR codes will continue to work, but additional scans beyond 100 per QR code will require additional credits. You can purchase more credits anytime.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Can I upgrade or downgrade my plan?</h3>
          <p class="text-gray-600">Since we use a credit system, there are no plans to upgrade or downgrade. Simply purchase more credits when you need them.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Do you offer refunds?</h3>
          <p class="text-gray-600">Yes, we offer a 30-day money-back guarantee. If you're not satisfied with our service, contact us for a full refund.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Is there a free trial?</h3>
          <p class="text-gray-600">You can start with our Starter plan for just $19 to test our service. We also offer a 30-day money-back guarantee on all purchases.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-gradient-to-br from-primary to-secondary">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
        Choose your plan and start creating professional QR codes with detailed analytics today.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
        <a href="/tool/qr-code-generator" class="inline-flex items-center px-8 py-4 bg-white text-primary font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg shadow-lg">
          Start with Professional
        </a>
        <a href="/tool/qr-code-generator" class="inline-flex items-center px-8 py-4 bg-white/10 text-white font-semibold rounded-lg hover:bg-white/20 transition-colors text-lg border border-white/20">
          Try Starter Plan
        </a>
      </div>

      <p class="text-white/80 text-sm">
        No subscriptions • Credits never expire • 30-day money-back guarantee
      </p>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4 text-center">
      <div class="bg-slate-50 rounded-xl p-8 max-w-2xl mx-auto">
        <h3 class="text-2xl font-bold text-gray-900 mb-4">Need Help Choosing?</h3>
        <p class="text-gray-600 mb-6">
          Our team is here to help you find the perfect plan for your needs.
        </p>
        <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg font-semibold hover:bg-primary/90 transition-colors">
          Contact Support
        </a>
      </div>
    </div>
  </section>
</Layout>